[gd_scene load_steps=2 format=3 uid="uid://ms20ikrcel5"]

[ext_resource type="Script" uid="uid://bw7pqrj84wnm3" path="res://scripts/menu.gd" id="1_yqeox"]

[node name="Menu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_yqeox")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.247059, 0.4, 0.337255, 1)

[node name="Label" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -84.5
offset_top = 63.0
offset_right = 84.5
offset_bottom = 176.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 82
text = "Dungeon"

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = -152.0
offset_right = 138.0
offset_bottom = -30.0
grow_vertical = 0

[node name="Button" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_type_variation = &"FlatButton"
theme_override_colors/font_hover_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 21
text = "Iniciar"
alignment = 0

[node name="Button2" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_type_variation = &"FlatButton"
theme_override_colors/font_hover_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 21
text = "Opções"
alignment = 0

[node name="Button3" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_type_variation = &"FlatButton"
theme_override_colors/font_hover_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 21
text = "Sair"
alignment = 0

[connection signal="pressed" from="VBoxContainer/Button" to="." method="_on_button_pressed"]
[connection signal="pressed" from="VBoxContainer/Button2" to="." method="_on_button_2_pressed"]
[connection signal="pressed" from="VBoxContainer/Button3" to="." method="_on_button_3_pressed"]
